import { cn } from "@/lib/utils";

interface AdaptiveTextProps {
  children: React.ReactNode;
  className?: string;
  variant?: "heading" | "subheading" | "body" | "caption";
}

export function AdaptiveText({ 
  children, 
  className, 
  variant = "body" 
}: AdaptiveTextProps) {
  const baseClasses = {
    heading: "font-medium text-gray-800 dark:text-gray-100",
    subheading: "text-gray-700 dark:text-gray-200",
    body: "text-gray-600 dark:text-gray-300",
    caption: "text-gray-500 dark:text-gray-400 text-sm"
  };

  return (
    <span className={cn(baseClasses[variant], className)}>
      {children}
    </span>
  );
}