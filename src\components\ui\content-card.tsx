import { cn } from '@/lib/utils';
import React from 'react';

interface ContentCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export function ContentCard({ children, className, ...props }: ContentCardProps) {
  return (
    <div 
      className={cn(
        "glass-card p-6 mb-6 border border-white/20 dark:border-blue-500/20",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}