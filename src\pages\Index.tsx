
import { useState } from 'react';
import { Mi<PERSON>, MessageCircle, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import Layout from '@/components/Layout';

const Index = () => {
  const [genzMode, setGenzMode] = useState(false);

  return (
    <Layout>
      <div className="page-container">
        {/* Gen Z Mode Toggle */}
        <section className="card card-spacing" aria-labelledby="genZ-mode-heading">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 id="genZ-mode-heading" className="font-medium text-gray-700 dark:text-gray-300 text-sm">
                Gen Z Mode
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {genzMode ? "vibes ✨" : "switch to casual tone"}
              </p>
            </div>
            <Switch
              checked={genzMode}
              onCheckedChange={setGenzMode}
              aria-labelledby="genZ-mode-heading"
              aria-describedby="genZ-mode-description"
            />
          </div>
          <div id="genZ-mode-description" className="sr-only">
            Toggle between casual Gen Z language and standard meditation language
          </div>
        </section>

        {/* Welcome Card */}
        <section className="card card-spacing text-center" aria-labelledby="welcome-heading">
          <div className="icon-container mx-auto mb-4" aria-hidden="true">
            <Sparkles className="text-white" size={20} />
          </div>
          <h1 id="welcome-heading" className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">
            {genzMode ? "how are you feeling? ✨" : "how are you feeling today?"}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 leading-relaxed">
            {genzMode
              ? "let's vibe and find some inner peace 🧘‍♀️"
              : "take a moment to breathe and connect"}
          </p>
        </section>

        {/* Mode Selection */}
        <section className="section-spacing" aria-labelledby="mode-selection-heading">
          <h2 id="mode-selection-heading" className="text-base font-medium text-gray-700 dark:text-gray-300 text-center mb-6">
            {genzMode ? "how do you wanna meditate?" : "choose your meditation style"}
          </h2>

          <div className="space-y-4" role="group" aria-labelledby="mode-selection-heading">
            {/* Voice Mode */}
            <button
              className="card hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer group w-full text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label={genzMode ? "Select voice mode with soothing voice vibes" : "Select voice guided meditation with gentle voice guidance"}
            >
              <div className="flex items-center space-x-4">
                <div className="icon-container group-hover:bg-blue-700 dark:group-hover:bg-blue-400 transition-colors duration-200" aria-hidden="true">
                  <Mic className="text-white" size={20} />
                </div>
                <div className="flex-1">
                  <h3 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {genzMode ? "voice mode 🎧" : "voice guided"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {genzMode
                      ? "soothing voice vibes"
                      : "gentle voice guidance"}
                  </p>
                </div>
              </div>
            </button>

            {/* Text Mode */}
            <button
              className="card hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer group w-full text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label={genzMode ? "Select text mode to read at your own pace" : "Select text based meditation with written guidance"}
            >
              <div className="flex items-center space-x-4">
                <div className="icon-container group-hover:bg-blue-700 dark:group-hover:bg-blue-400 transition-colors duration-200" aria-hidden="true">
                  <MessageCircle className="text-white" size={20} />
                </div>
                <div className="flex-1">
                  <h3 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {genzMode ? "text mode 📱" : "text based"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {genzMode
                      ? "read at your own pace"
                      : "written guidance"}
                  </p>
                </div>
              </div>
            </button>
          </div>
        </section>

        {/* CTA Button */}
        <div className="text-center section-spacing">
          <Button
            size="lg"
            className="btn-primary w-full py-4 text-base font-medium"
            aria-label={genzMode ? "Start your meditation session with casual vibes" : "Begin your meditation journey"}
          >
            {genzMode ? "let's start ✨" : "begin journey"}
          </Button>
        </div>

        {/* Quote */}
        <div className="text-center">
          <div className="card">
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              {genzMode
                ? "\"peace begins with good vibes\" ✨"
                : "\"peace comes from within\" - buddha"}
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Index;
