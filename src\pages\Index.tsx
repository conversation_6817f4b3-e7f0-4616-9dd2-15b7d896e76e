
import { useState } from 'react';
import { Mi<PERSON>, MessageCircle, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import Layout from '@/components/Layout';

const Index = () => {
  const [genzMode, setGenzMode] = useState(false);

  return (
    <Layout>
      <div className="page-container">
        {/* Gen Z Mode Toggle */}
        <div className="card card-spacing">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-700 dark:text-gray-300 text-sm">Gen Z Mode</h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {genzMode ? "vibes ✨" : "switch to casual tone"}
              </p>
            </div>
            <Switch
              checked={genzMode}
              onCheckedChange={setGenzMode}
              className="data-[state=checked]:bg-blue-600"
              aria-label="Toggle Gen Z Mode"
            />
          </div>
        </div>

        {/* Welcome Card */}
        <div className="card card-spacing text-center">
          <div className="icon-container mx-auto mb-4">
            <Sparkles className="text-white" size={20} />
          </div>
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-3">
            {genzMode ? "how are you feeling? ✨" : "how are you feeling today?"}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 leading-relaxed">
            {genzMode
              ? "let's vibe and find some inner peace 🧘‍♀️"
              : "take a moment to breathe and connect"}
          </p>
        </div>

        {/* Mode Selection */}
        <div className="section-spacing">
          <h3 className="text-base font-medium text-gray-700 dark:text-gray-300 text-center mb-6">
            {genzMode ? "how do you wanna meditate?" : "choose your meditation style"}
          </h3>

          <div className="space-y-4">
            {/* Voice Mode */}
            <div className="card hover:border-blue-300 dark:hover:border-blue-600 transition-colors duration-200 cursor-pointer group">
              <div className="flex items-center space-x-4">
                <div className="icon-container group-hover:bg-blue-700 dark:group-hover:bg-blue-400 transition-colors duration-200">
                  <Mic className="text-white" size={20} />
                </div>
                <div className="flex-1">
                  <h4 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {genzMode ? "voice mode 🎧" : "voice guided"}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {genzMode
                      ? "soothing voice vibes"
                      : "gentle voice guidance"}
                  </p>
                </div>
              </div>
            </div>

            {/* Text Mode */}
            <div className="card hover:border-blue-300 dark:hover:border-blue-600 transition-colors duration-200 cursor-pointer group">
              <div className="flex items-center space-x-4">
                <div className="icon-container group-hover:bg-blue-700 dark:group-hover:bg-blue-400 transition-colors duration-200">
                  <MessageCircle className="text-white" size={20} />
                </div>
                <div className="flex-1">
                  <h4 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {genzMode ? "text mode 📱" : "text based"}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {genzMode
                      ? "read at your own pace"
                      : "written guidance"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <div className="text-center section-spacing">
          <Button
            className="btn-primary w-full py-4 text-base"
            aria-label="Begin meditation journey"
          >
            {genzMode ? "let's start ✨" : "begin journey"}
          </Button>
        </div>

        {/* Quote */}
        <div className="text-center">
          <div className="card">
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              {genzMode
                ? "\"peace begins with good vibes\" ✨"
                : "\"peace comes from within\" - buddha"}
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Index;
