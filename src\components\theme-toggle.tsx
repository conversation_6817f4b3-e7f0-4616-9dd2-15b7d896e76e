"use client";

import * as React from "react";
import { Moon, Sun } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useTheme } from "@/components/theme-provider";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <Button
      variant="outline"
      size="icon"
      className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-full w-9 h-9 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 focus:ring-blue-500 focus:border-blue-500"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      aria-label="Toggle theme"
      style={{
        backgroundColor: theme === "light" ? "white" : "rgb(31, 41, 55)",
        borderColor: theme === "light" ? "rgb(209, 213, 219)" : "rgb(75, 85, 99)",
        color: theme === "light" ? "rgb(75, 85, 99)" : "rgb(209, 213, 219)"
      }}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-gray-600 dark:text-gray-300" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-gray-600 dark:text-gray-300" />
    </Button>
  );
}