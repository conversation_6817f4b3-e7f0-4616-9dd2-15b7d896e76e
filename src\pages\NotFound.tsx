import { Link } from "react-router-dom";
import Layout from '@/components/Layout';
import { Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PageHeader } from '@/components/ui/page-header';

const NotFound = () => {
  return (
    <Layout>
      <div className="page-container flex flex-col items-center justify-center">
        {/* Header */}
        <div className="section-spacing">
          <div className="text-center mb-4">
            <h1 className="text-5xl font-light bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent mb-3">
              404
            </h1>
          </div>
          <PageHeader
            title=""
            subtitle="page not found"
          />
        </div>

        {/* Error Card */}
        <div className="glass-card text-center w-full">
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">The page you're looking for doesn't exist or has been moved.</p>

          <Button asChild className="btn-primary">
            <Link to="/" className="flex items-center">
              <Home size={18} className="mr-2" />
              Return to Home
            </Link>
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default NotFound;
