import { Link } from "react-router-dom";
import Layout from '@/components/Layout';
import { Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

const NotFound = () => {
  return (
    <Layout>
      <div className="page-container flex flex-col items-center justify-center">
        {/* Error Card */}
        <div className="card text-center w-full">
          <div className="mb-6">
            <h1 className="text-6xl font-light text-blue-600 dark:text-blue-400 mb-3">
              404
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">The page you're looking for doesn't exist or has been moved.</p>
          </div>

          <Button asChild className="btn-primary">
            <Link to="/" className="flex items-center">
              <Home size={18} className="mr-2" />
              Return to Home
            </Link>
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default NotFound;
