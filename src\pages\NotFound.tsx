import { Link } from "react-router-dom";
import Layout from '@/components/Layout';
import { Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

const NotFound = () => {
  return (
    <Layout>
      <div className="min-h-screen px-4 py-6 max-w-md mx-auto flex flex-col items-center justify-center">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-light bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent mb-3">
            404
          </h1>
          <p className="text-gray-500 text-sm font-light tracking-wide">page not found</p>
        </div>

        {/* Error Card */}
        <div className="glass-card p-6 mb-8 text-center border border-white/20 w-full">
          <p className="text-gray-600 text-sm mb-6">The page you're looking for doesn't exist or has been moved.</p>
          
          <Button asChild className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0 rounded-2xl">
            <Link to="/" className="flex items-center">
              <Home size={18} className="mr-2" />
              Return to Home
            </Link>
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default NotFound;
