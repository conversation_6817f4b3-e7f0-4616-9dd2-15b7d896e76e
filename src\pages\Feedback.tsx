
import { useState } from 'react';
import { Star, Send, Heart, MessageCircle, Lightbulb, Bug } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { PageHeader } from '@/components/ui/page-header';
import { toast } from 'sonner';
import Layout from '@/components/Layout';

const Feedback = () => {
  const [rating, setRating] = useState(0);
  const [feedbackType, setFeedbackType] = useState('general');
  const [message, setMessage] = useState('');
  const [hoveredStar, setHoveredStar] = useState(0);

  const feedbackTypes = [
    { value: 'general', label: 'General Feedback', icon: MessageCircle },
    { value: 'feature', label: 'Feature Request', icon: Lightbulb },
    { value: 'bug', label: 'Report a Bug', icon: Bug },
    { value: 'appreciation', label: 'Show Appreciation', icon: Heart }
  ];

  const handleSubmit = () => {
    if (rating === 0) {
      toast.error('Please provide a rating');
      return;
    }
    if (message.trim() === '') {
      toast.error('Please write your feedback');
      return;
    }

    toast.success('Thank you for your feedback! Samaya appreciates your input. 🙏');
    setRating(0);
    setMessage('');
    setFeedbackType('general');
  };

  return (
    <Layout>
      <div className="page-container">
        {/* Header */}
        <div className="section-spacing">
          <PageHeader
            title="Feedback"
            subtitle="help samaya grow and improve"
          />
        </div>

        {/* Feedback Form */}
        <div className="glass-card card-spacing">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Heart className="text-white" size={24} strokeWidth={1.5} />
            </div>
            <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
              how was your experience with samaya?
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 font-light leading-relaxed">
              your feedback helps create a more peaceful experience for everyone
            </p>
          </div>

          {/* Star Rating */}
          <div className="mb-6">
            <Label className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3 block">
              Rate your experience
            </Label>
            <div className="flex justify-center space-x-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredStar(star)}
                  onMouseLeave={() => setHoveredStar(0)}
                  className="transition-colors duration-200"
                >
                  <Star
                    size={28}
                    className={`${
                      star <= (hoveredStar || rating)
                        ? 'text-blue-500 fill-blue-500'
                        : 'text-gray-300 dark:text-gray-600'
                    } transition-colors duration-200`}
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Feedback Type */}
          <div className="mb-6">
            <Label className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3 block">
              What type of feedback is this?
            </Label>
            <RadioGroup value={feedbackType} onValueChange={setFeedbackType}>
              <div className="grid grid-cols-2 gap-3">
                {feedbackTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <div key={type.value} className="flex items-center space-x-2">
                      <RadioGroupItem
                        value={type.value}
                        id={type.value}
                        className="text-blue-500"
                      />
                      <Label
                        htmlFor={type.value}
                        className="flex items-center space-x-2 cursor-pointer text-xs"
                      >
                        <Icon size={14} className="text-blue-500" />
                        <span>{type.label}</span>
                      </Label>
                    </div>
                  );
                })}
              </div>
            </RadioGroup>
          </div>

          {/* Message */}
          <div className="mb-6">
            <Label htmlFor="message" className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3 block">
              Tell us more
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Share your thoughts, suggestions, or any issues you've encountered..."
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 min-h-[100px] resize-none text-sm rounded-xl"
            />
          </div>

          {/* Submit Button */}
          <Button
            onClick={handleSubmit}
            className="btn-primary w-full"
          >
            <Send size={16} className="mr-2" />
            Send Feedback
          </Button>
        </div>

        {/* Recent Feedback */}
        <div className="glass-card">
          <h3 className="text-base font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <MessageCircle className="mr-2 text-blue-500" size={18} />
            Community Feedback
          </h3>
          <div className="space-y-4">
            <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-xl">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xs font-medium">A</span>
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">Anonymous</span>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star key={star} size={10} className="text-blue-500 fill-blue-500" />
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-xs leading-relaxed">
                    "Samaya's voice is so calming and the meditation guidance feels very natural. Love the Gen Z mode!"
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50/50 dark:bg-blue-900/20 p-4 rounded-xl">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xs font-medium">M</span>
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">Mindful User</span>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4].map((star) => (
                        <Star key={star} size={10} className="text-blue-500 fill-blue-500" />
                      ))}
                      <Star size={10} className="text-gray-300 dark:text-gray-600" />
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-xs leading-relaxed">
                    "The glass design is beautiful and very peaceful. Would love more session categories!"
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Feedback;
