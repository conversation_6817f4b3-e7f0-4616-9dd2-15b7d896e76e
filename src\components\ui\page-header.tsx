import React from 'react';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  rightElement?: React.ReactNode;
}

export function PageHeader({ title, subtitle, rightElement }: PageHeaderProps) {
  return (
    <div className="text-center mb-8 relative">
      {rightElement && (
        <div className="absolute right-0 top-0">
          {rightElement}
        </div>
      )}
      <h1 className="text-4xl font-light bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent mb-2">
        {title}
      </h1>
      {subtitle && (
        <p className="text-gray-500 dark:text-gray-400 text-sm font-light tracking-wide">
          {subtitle}
        </p>
      )}
    </div>
  );
}