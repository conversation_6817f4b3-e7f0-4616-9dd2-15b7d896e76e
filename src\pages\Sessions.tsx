
import { useState } from 'react';
import { Play, Clock, Heart, MoreVertical, Calendar as CalendarIcon, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Layout from '@/components/Layout';

const Sessions = () => {
  const [selectedSession, setSelectedSession] = useState<string | null>(null);

  const recentSessions = [
    {
      id: '1',
      title: 'Morning Breath Awareness',
      duration: '10 min',
      date: 'Today, 8:00 AM',
      type: 'Breathing',
      completed: true
    },
    {
      id: '2',
      title: 'Stress Relief Meditation',
      duration: '15 min',
      date: 'Yesterday, 6:30 PM',
      type: 'Relaxation',
      completed: true
    },
    {
      id: '3',
      title: 'Loving Kindness Practice',
      duration: '12 min',
      date: '2 days ago, 7:00 AM',
      type: 'Compassion',
      completed: true
    }
  ];

  const recommendedSessions = [
    {
      id: '4',
      title: 'Focus & Clarity',
      duration: '8 min',
      description: 'Perfect for when you need mental clarity',
      type: 'Focus',
      difficulty: 'Beginner'
    },
    {
      id: '5',
      title: 'Body Scan Relaxation',
      duration: '20 min',
      description: 'Release tension from head to toe',
      type: 'Body Scan',
      difficulty: 'Intermediate'
    },
    {
      id: '6',
      title: 'Sleep Preparation',
      duration: '15 min',
      description: 'Wind down for peaceful sleep',
      type: 'Sleep',
      difficulty: 'Beginner'
    }
  ];

  const weeklyProgress = [
    { day: 'Mon', completed: true, duration: 10 },
    { day: 'Tue', completed: true, duration: 15 },
    { day: 'Wed', completed: false, duration: 0 },
    { day: 'Thu', completed: true, duration: 8 },
    { day: 'Fri', completed: true, duration: 12 },
    { day: 'Sat', completed: false, duration: 0 },
    { day: 'Sun', completed: true, duration: 20 }
  ];

  return (
    <Layout>
      <div className="min-h-screen px-4 py-6 max-w-md mx-auto">
        {/* Recent Sessions */}
        <div className="mb-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-light bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent mb-2">
              Sessions
            </h1>
            <p className="text-gray-500 text-sm font-light">track your meditation journey</p>
          </div>

          {/* Weekly Progress */}
          <div className="glass-card p-5 mb-6 border border-white/20">
            <h3 className="text-base font-medium text-gray-800 mb-4 flex items-center">
              <CalendarIcon className="mr-2 text-blue-500" size={18} />
              This Week's Progress
            </h3>
            <div className="flex justify-between items-end space-x-2">
              {weeklyProgress.map((day, index) => (
                <div key={index} className="flex flex-col items-center flex-1">
                  <div
                    className={`w-full rounded-lg mb-2 transition-all duration-300 ${
                      day.completed
                        ? 'bg-gradient-to-t from-blue-500 to-blue-400'
                        : 'bg-gray-200'
                    }`}
                    style={{
                      height: day.completed ? `${Math.max(day.duration * 3, 20)}px` : '20px'
                    }}
                  ></div>
                  <span className="text-xs font-medium text-gray-600">{day.day}</span>
                  {day.completed && (
                    <span className="text-xs text-blue-600 font-medium">{day.duration}m</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="recent" className="w-full">
            <TabsList className="grid w-full grid-cols-2 glass-card border border-white/20">
              <TabsTrigger value="recent" className="text-gray-700 text-sm">Recent</TabsTrigger>
              <TabsTrigger value="recommended" className="text-gray-700 text-sm">Recommended</TabsTrigger>
            </TabsList>

            <TabsContent value="recent" className="space-y-3 mt-4">
              {recentSessions.map((session) => (
                <div
                  key={session.id}
                  className="glass-card p-4 border border-white/20"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                        <Heart className="text-white" size={16} strokeWidth={1.5} />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-800 text-sm">{session.title}</h4>
                        <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
                          <Clock size={12} />
                          <span>{session.duration}</span>
                          <span>•</span>
                          <span>{session.date}</span>
                        </div>
                        <span className="inline-block bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded-full mt-1">
                          {session.type}
                        </span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="text-gray-400">
                      <MoreVertical size={14} />
                    </Button>
                  </div>
                </div>
              ))}
            </TabsContent>

            <TabsContent value="recommended" className="space-y-3 mt-4">
              {recommendedSessions.map((session) => (
                <div
                  key={session.id}
                  className="glass-card p-4 border border-white/20 cursor-pointer"
                  onClick={() => setSelectedSession(session.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                        <Play className="text-white" size={16} strokeWidth={1.5} />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-800 text-sm">{session.title}</h4>
                        <p className="text-xs text-gray-500 mb-1">{session.description}</p>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Clock size={12} />
                          <span>{session.duration}</span>
                          <span>•</span>
                          <span className="text-blue-600 font-medium">{session.difficulty}</span>
                        </div>
                        <span className="inline-block bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded-full mt-1">
                          {session.type}
                        </span>
                      </div>
                    </div>
                    <Button className="bg-blue-500 hover:bg-blue-600 text-white text-xs px-3 py-1 rounded-xl">
                      Start
                    </Button>
                  </div>
                </div>
              ))}
            </TabsContent>
          </Tabs>

          {/* Quick Start */}
          <div className="mt-6 text-center">
            <Button className="glass-button text-blue-600 font-medium px-6 py-3 text-sm border border-white/20">
              <Sparkles size={16} className="mr-2" />
              Start Custom Session
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Sessions;
