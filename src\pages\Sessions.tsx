
import { useState } from 'react';
import { Play, Clock, Heart, MoreVertical, Calendar as CalendarIcon, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PageHeader } from '@/components/ui/page-header';
import Layout from '@/components/Layout';

const Sessions = () => {
  const [selectedSession, setSelectedSession] = useState<string | null>(null);

  const recentSessions = [
    {
      id: '1',
      title: 'Morning Breath Awareness',
      duration: '10 min',
      date: 'Today, 8:00 AM',
      type: 'Breathing',
      completed: true
    },
    {
      id: '2',
      title: 'Stress Relief Meditation',
      duration: '15 min',
      date: 'Yesterday, 6:30 PM',
      type: 'Relaxation',
      completed: true
    },
    {
      id: '3',
      title: 'Loving Kindness Practice',
      duration: '12 min',
      date: '2 days ago, 7:00 AM',
      type: 'Compassion',
      completed: true
    }
  ];

  const recommendedSessions = [
    {
      id: '4',
      title: 'Focus & Clarity',
      duration: '8 min',
      description: 'Perfect for when you need mental clarity',
      type: 'Focus',
      difficulty: 'Beginner'
    },
    {
      id: '5',
      title: 'Body Scan Relaxation',
      duration: '20 min',
      description: 'Release tension from head to toe',
      type: 'Body Scan',
      difficulty: 'Intermediate'
    },
    {
      id: '6',
      title: 'Sleep Preparation',
      duration: '15 min',
      description: 'Wind down for peaceful sleep',
      type: 'Sleep',
      difficulty: 'Beginner'
    }
  ];

  const weeklyProgress = [
    { day: 'Mon', completed: true, duration: 10 },
    { day: 'Tue', completed: true, duration: 15 },
    { day: 'Wed', completed: false, duration: 0 },
    { day: 'Thu', completed: true, duration: 8 },
    { day: 'Fri', completed: true, duration: 12 },
    { day: 'Sat', completed: false, duration: 0 },
    { day: 'Sun', completed: true, duration: 20 }
  ];

  return (
    <Layout>
      <div className="page-container">
        {/* Header */}
        <div className="section-spacing">
          <PageHeader
            title="Sessions"
            subtitle="track your meditation journey"
          />
        </div>

        {/* Weekly Progress */}
        <div className="glass-card card-spacing">
          <h3 className="text-base font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <CalendarIcon className="mr-2 text-blue-500" size={18} />
            This Week's Progress
          </h3>
          <div className="flex justify-between items-end space-x-2">
            {weeklyProgress.map((day, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div
                  className={`w-full rounded-lg mb-2 transition-all duration-300 ${
                    day.completed
                      ? 'bg-gradient-to-t from-blue-500 to-blue-600'
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                  style={{
                    height: day.completed ? `${Math.max(day.duration * 3, 20)}px` : '20px'
                  }}
                ></div>
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">{day.day}</span>
                {day.completed && (
                  <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">{day.duration}m</span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="recent" className="w-full section-spacing">
          <TabsList className="grid w-full grid-cols-2 glass-card">
            <TabsTrigger value="recent" className="text-gray-700 dark:text-gray-300 text-sm">Recent</TabsTrigger>
            <TabsTrigger value="recommended" className="text-gray-700 dark:text-gray-300 text-sm">Recommended</TabsTrigger>
          </TabsList>

          <TabsContent value="recent" className="space-y-4 mt-4">
            {recentSessions.map((session) => (
              <div
                key={session.id}
                className="glass-card"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                      <Heart className="text-white" size={16} strokeWidth={1.5} />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm">{session.title}</h4>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <Clock size={12} />
                        <span>{session.duration}</span>
                        <span>•</span>
                        <span>{session.date}</span>
                      </div>
                      <span className="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs px-2 py-0.5 rounded-full mt-1">
                        {session.type}
                      </span>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" className="text-gray-400 dark:text-gray-500">
                    <MoreVertical size={14} />
                  </Button>
                </div>
              </div>
            ))}
          </TabsContent>

          <TabsContent value="recommended" className="space-y-4 mt-4">
            {recommendedSessions.map((session) => (
              <div
                key={session.id}
                className="glass-card cursor-pointer"
                onClick={() => setSelectedSession(session.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                      <Play className="text-white" size={16} strokeWidth={1.5} />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm">{session.title}</h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">{session.description}</p>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <Clock size={12} />
                        <span>{session.duration}</span>
                        <span>•</span>
                        <span className="text-blue-600 dark:text-blue-400 font-medium">{session.difficulty}</span>
                      </div>
                      <span className="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs px-2 py-0.5 rounded-full mt-1">
                        {session.type}
                      </span>
                    </div>
                  </div>
                  <Button className="btn-primary text-xs px-3 py-1">
                    Start
                  </Button>
                </div>
              </div>
            ))}
          </TabsContent>
        </Tabs>

        {/* Quick Start */}
        <div className="text-center">
          <Button className="btn-secondary">
            <Sparkles size={16} className="mr-2" />
            Start Custom Session
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default Sessions;
