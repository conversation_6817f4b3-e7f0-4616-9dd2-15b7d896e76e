
import { useState } from 'react';
import { User, Settings, Award, Calendar, Clock, Heart, Edit3 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PageHeader } from '@/components/ui/page-header';
import Layout from '@/components/Layout';
import { ThemeToggle } from '@/components/theme-toggle';

const Profile = () => {
  const [name, setName] = useState('Peaceful Soul');
  const [email, setEmail] = useState('<EMAIL>');
  const [isEditing, setIsEditing] = useState(false);

  const stats = [
    { icon: Calendar, label: 'Days Meditating', value: '42', color: 'from-blue-500 to-blue-600' },
    { icon: Clock, label: 'Total Minutes', value: '1,260', color: 'from-blue-400 to-blue-500' },
    { icon: Heart, label: 'Sessions Completed', value: '89', color: 'from-blue-500 to-blue-600' },
    { icon: Award, label: 'Current Streak', value: '7 days', color: 'from-blue-400 to-blue-500' }
  ];

  const achievements = [
    { emoji: '🏆', title: 'Weekly Warrior', desc: 'Meditated 7 days in a row' },
    { emoji: '🧘‍♀️', title: 'Mindful Beginner', desc: 'Completed your first 10 sessions' },
    { emoji: '⭐', title: 'Time Master', desc: 'Reached 1000 minutes of meditation' }
  ];

  return (
    <Layout>
      <div className="min-h-screen px-4 py-6 max-w-md mx-auto">
        {/* Header */}
        <PageHeader
          title="Profile"
          subtitle="track your journey"
          rightElement={<ThemeToggle />}
        />
        
        {/* Profile Card */}
        <div className="glass-card p-6 mb-6 border border-white/20">
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mb-4">
              <User className="text-white" size={32} strokeWidth={1.5} />
            </div>
            
            {isEditing ? (
              <div className="space-y-4 w-full max-w-sm">
                <div>
                  <Label htmlFor="name" className="text-sm text-gray-600 dark:text-gray-400">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="glass-card border-white/30 mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-sm text-gray-600 dark:text-gray-400">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="glass-card border-white/30 mt-1"
                  />
                </div>
                <div className="flex space-x-3 pt-2">
                  <Button
                    onClick={() => setIsEditing(false)}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 text-white border-0 rounded-xl"
                  >
                    Save
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    variant="outline"
                    className="flex-1 glass-button rounded-xl"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-1">{name}</h2>
                <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">{email}</p>
                <Button
                  onClick={() => setIsEditing(true)}
                  className="glass-button text-blue-600 dark:text-blue-400 text-sm px-4 py-2"
                >
                  <Edit3 size={14} className="mr-2" />
                  Edit Profile
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-3 mb-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="glass-card p-6 text-center border border-white/20"
              >
                <div className={`w-10 h-10 bg-gradient-to-br ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-3`}>
                  <Icon className="text-white" size={16} strokeWidth={1.5} />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">{stat.value}</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400 font-light">{stat.label}</p>
              </div>
            );
          })}
        </div>

        {/* Achievements */}
        <div className="glass-card p-5 mb-6 border border-white/20">
          <h3 className="text-base font-medium text-gray-800 mb-4 flex items-center">
            <Award className="mr-2 text-blue-500" size={18} />
            Recent Achievements
          </h3>
          <div className="space-y-3">
            {achievements.map((achievement, index) => (
              <div key={index} className="flex items-center p-3 bg-blue-50/50 rounded-xl">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-3 text-lg">
                  {achievement.emoji}
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 text-sm">{achievement.title}</h4>
                  <p className="text-xs text-gray-500">{achievement.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Preferences */}
        <div className="glass-card p-5 border border-white/20">
          <h3 className="text-base font-medium text-gray-800 mb-4">Preferences</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 text-sm">Daily Reminder</span>
              <span className="text-blue-600 font-medium text-sm">8:00 AM</span>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 text-sm">Session Length</span>
              <span className="text-blue-600 font-medium text-sm">10 minutes</span>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 text-sm">Preferred Voice</span>
              <span className="text-blue-600 font-medium text-sm">Samaya</span>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Profile;
