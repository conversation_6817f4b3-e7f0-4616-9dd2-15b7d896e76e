
import { useState } from 'react';
import { User, Settings, Award, Calendar, Clock, Heart, Edit3 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PageHeader } from '@/components/ui/page-header';
import Layout from '@/components/Layout';
import { ThemeToggle } from '@/components/theme-toggle';

const Profile = () => {
  const [name, setName] = useState('Peaceful Soul');
  const [email, setEmail] = useState('<EMAIL>');
  const [isEditing, setIsEditing] = useState(false);

  const stats = [
    { icon: Calendar, label: 'Days Meditating', value: '42', color: 'from-blue-500 to-blue-600' },
    { icon: Clock, label: 'Total Minutes', value: '1,260', color: 'from-blue-400 to-blue-500' },
    { icon: Heart, label: 'Sessions Completed', value: '89', color: 'from-blue-500 to-blue-600' },
    { icon: Award, label: 'Current Streak', value: '7 days', color: 'from-blue-400 to-blue-500' }
  ];

  const achievements = [
    { emoji: '🏆', title: 'Weekly Warrior', desc: 'Meditated 7 days in a row' },
    { emoji: '🧘‍♀️', title: 'Mindful Beginner', desc: 'Completed your first 10 sessions' },
    { emoji: '⭐', title: 'Time Master', desc: 'Reached 1000 minutes of meditation' }
  ];

  return (
    <Layout>
      <div className="page-container">
        {/* Header */}
        <div className="section-spacing">
          <PageHeader
            title="Profile"
            subtitle="track your journey"
            rightElement={<ThemeToggle />}
          />
        </div>

        {/* Profile Card */}
        <div className="glass-card card-spacing">
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mb-4">
              <User className="text-white" size={32} strokeWidth={1.5} />
            </div>
            
            {isEditing ? (
              <div className="space-y-4 w-full max-w-sm">
                <div>
                  <Label htmlFor="name" className="text-sm text-gray-600 dark:text-gray-400">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="glass-card border-white/30 mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-sm text-gray-600 dark:text-gray-400">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="glass-card border-white/30 mt-1"
                  />
                </div>
                <div className="flex space-x-3 pt-2">
                  <Button
                    onClick={() => setIsEditing(false)}
                    className="btn-primary flex-1"
                  >
                    Save
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    className="btn-secondary flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-1">{name}</h2>
                <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">{email}</p>
                <Button
                  onClick={() => setIsEditing(true)}
                  className="btn-secondary text-sm"
                >
                  <Edit3 size={14} className="mr-2" />
                  Edit Profile
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4 card-spacing">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="glass-card text-center"
              >
                <div className={`w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3`}>
                  <Icon className="text-white" size={16} strokeWidth={1.5} />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">{stat.value}</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400 font-light">{stat.label}</p>
              </div>
            );
          })}
        </div>

        {/* Achievements */}
        <div className="glass-card card-spacing">
          <h3 className="text-base font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <Award className="mr-2 text-blue-500" size={18} />
            Recent Achievements
          </h3>
          <div className="space-y-3">
            {achievements.map((achievement, index) => (
              <div key={index} className="flex items-center p-3 bg-blue-50/50 dark:bg-blue-900/20 rounded-xl">
                <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-full flex items-center justify-center mr-3 text-lg">
                  {achievement.emoji}
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm">{achievement.title}</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{achievement.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Preferences */}
        <div className="glass-card">
          <h3 className="text-base font-medium text-gray-800 dark:text-gray-200 mb-4">Preferences</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Daily Reminder</span>
              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">8:00 AM</span>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Session Length</span>
              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">10 minutes</span>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Preferred Voice</span>
              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">Samaya</span>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Profile;
