
import { useState } from 'react';
import { User, Settings, Award, Calendar, Clock, Heart, Edit3 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import Layout from '@/components/Layout';
import { ThemeToggle } from '@/components/theme-toggle';

const Profile = () => {
  const [name, setName] = useState('Peaceful Soul');
  const [email, setEmail] = useState('<EMAIL>');
  const [isEditing, setIsEditing] = useState(false);

  const stats = [
    { icon: Calendar, label: 'Days Meditating', value: '42', color: 'from-blue-500 to-blue-600' },
    { icon: Clock, label: 'Total Minutes', value: '1,260', color: 'from-blue-400 to-blue-500' },
    { icon: Heart, label: 'Sessions Completed', value: '89', color: 'from-blue-500 to-blue-600' },
    { icon: Award, label: 'Current Streak', value: '7 days', color: 'from-blue-400 to-blue-500' }
  ];

  const achievements = [
    { emoji: '🏆', title: 'Weekly Warrior', desc: 'Meditated 7 days in a row' },
    { emoji: '🧘‍♀️', title: 'Mindful Beginner', desc: 'Completed your first 10 sessions' },
    { emoji: '⭐', title: 'Time Master', desc: 'Reached 1000 minutes of meditation' }
  ];

  return (
    <Layout>
      <div className="page-container">
        {/* Theme Toggle */}
        <div className="flex justify-end mb-6">
          <ThemeToggle />
        </div>

        {/* Profile Card */}
        <section className="card card-spacing" aria-labelledby="profile-heading">
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center mb-4 shadow-lg" aria-hidden="true">
              <User className="text-white" size={32} strokeWidth={1.5} />
            </div>
            
            {isEditing ? (
              <div className="space-y-4 w-full max-w-sm">
                <div>
                  <Label htmlFor="name" className="text-sm text-gray-600 dark:text-gray-400">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-sm text-gray-600 dark:text-gray-400">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div className="flex space-x-3 pt-2">
                  <Button
                    onClick={() => setIsEditing(false)}
                    className="btn-primary flex-1"
                  >
                    Save
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    className="btn-secondary flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <h1 id="profile-heading" className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-1">{name}</h1>
                <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">{email}</p>
                <Button
                  onClick={() => setIsEditing(true)}
                  className="btn-accent text-sm"
                  aria-label="Edit your profile information"
                >
                  <Edit3 size={14} className="mr-2" aria-hidden="true" />
                  Edit Profile
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4 card-spacing">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="card text-center"
              >
                <div className="icon-container-sm mx-auto mb-3">
                  <Icon className="text-white" size={16} strokeWidth={1.5} />
                </div>
                <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">{stat.value}</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">{stat.label}</p>
              </div>
            );
          })}
        </div>

        {/* Achievements */}
        <div className="card card-spacing">
          <h3 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <Award className="mr-2 text-blue-500 dark:text-blue-400" size={18} />
            Recent Achievements
          </h3>
          <div className="space-y-3">
            {achievements.map((achievement, index) => (
              <div key={index} className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-full flex items-center justify-center mr-3 text-lg">
                  {achievement.emoji}
                </div>
                <div>
                  <h4 className="font-medium text-gray-700 dark:text-gray-300 text-sm">{achievement.title}</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{achievement.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Preferences */}
        <div className="card">
          <h3 className="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">Preferences</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Daily Reminder</span>
              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">8:00 AM</span>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Session Length</span>
              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">10 minutes</span>
            </div>
            <div className="flex items-center justify-between py-1">
              <span className="text-gray-700 dark:text-gray-300 text-sm">Preferred Voice</span>
              <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">Samaya</span>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Profile;
