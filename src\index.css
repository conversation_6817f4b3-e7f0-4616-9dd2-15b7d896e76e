
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 51 65 85;

    --card: 255 255 255;
    --card-foreground: 51 65 85;

    --popover: 255 255 255;
    --popover-foreground: 51 65 85;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 241 245 249;
    --secondary-foreground: 51 65 85;

    --muted: 248 250 252;
    --muted-foreground: 100 116 139;

    --accent: 219 234 254;
    --accent-foreground: 30 64 175;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 1rem;

    /* Custom meditation app colors */
    --meditation-blue: 147 197 253;
    --meditation-light-blue: 219 234 254;
    --meditation-soft-white: 248 250 252;
    --meditation-glass: rgba(255, 255, 255, 0.1);
    --meditation-glass-border: rgba(255, 255, 255, 0.2);
  }

  .dark {
    --background: 17 24 39; /* Equivalent to bg-gray-900 */
    --foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --card: 31 41 55; /* Equivalent to bg-gray-800 */
    --card-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --popover: 31 41 55; /* Equivalent to bg-gray-800 */
    --popover-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --primary: 96 165 250; /* Equivalent to blue-400 */
    --primary-foreground: 17 24 39; /* Equivalent to bg-gray-900 for contrast */

    --secondary: 55 65 81; /* Equivalent to bg-gray-700 */
    --secondary-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --muted: 55 65 81; /* Equivalent to bg-gray-700 */
    --muted-foreground: 156 163 175; /* Equivalent to text-gray-400 */

    --accent: 59 130 246; /* Equivalent to blue-500 */
    --accent-foreground: 249 250 251; /* Equivalent to text-gray-50 */

    --destructive: 220 38 38; /* Equivalent to red-600 */
    --destructive-foreground: 249 250 251; /* Equivalent to text-gray-50 */

    --border: 55 65 81; /* Equivalent to border-gray-700 */
    --input: 55 65 81; /* Equivalent to bg-gray-700 for inputs */
    --ring: 96 165 250; /* Equivalent to blue-400 */

    /* Dark mode meditation colors - adjusted for better dark theme */
    --meditation-blue: 59 130 246; /* blue-500 */
    --meditation-light-blue: 37 99 235; /* blue-600 */
    --meditation-soft-white: 17 24 39; /* gray-900 */
    --meditation-glass: rgba(31, 41, 55, 0.5); /* gray-800 with 0.5 opacity */
    --meditation-glass-border: rgba(55, 65, 81, 0.7); /* gray-700 with 0.7 opacity */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 min-h-screen;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
  }
}

@layer components {
  /* Consistent container for all pages - wider like feedback page */
  .page-container {
    @apply min-h-screen px-6 py-6 max-w-xl mx-auto;
  }

  /* Clean card styling - calm and minimal */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-2xl border border-gray-100 dark:border-gray-700 p-6 w-full shadow-sm;
  }

  /* Primary button - calm blue styling */
  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-xl px-6 py-3 font-medium transition-colors duration-200;
  }

  /* Secondary button - calm outline styling */
  .btn-secondary {
    @apply bg-transparent border border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-xl px-6 py-3 font-medium transition-colors duration-200;
  }

  /* Accent button - very subtle styling */
  .btn-accent {
    @apply bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-xl px-6 py-3 font-medium transition-colors duration-200;
  }

  /* Consistent section spacing */
  .section-spacing {
    @apply mb-8;
  }

  /* Consistent card spacing */
  .card-spacing {
    @apply mb-6;
  }

  /* Icon container - calm styling */
  .icon-container {
    @apply w-12 h-12 bg-blue-500 dark:bg-blue-600 rounded-xl flex items-center justify-center;
  }

  /* Small icon container */
  .icon-container-sm {
    @apply w-10 h-10 bg-blue-500 dark:bg-blue-600 rounded-lg flex items-center justify-center;
  }
}

/* Simple fade-in animation only */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* AGGRESSIVE OVERRIDE: Remove ALL yellow colors */
* {
  /* Remove any yellow focus rings */
  --tw-ring-color: rgb(59 130 246) !important; /* blue-500 */
}

/* Force all switches to be blue */
button[role="switch"][data-state="checked"],
[data-state="checked"] {
  background-color: rgb(59 130 246) !important; /* blue-500 */
  border-color: rgb(59 130 246) !important; /* blue-500 */
}

button[role="switch"][data-state="unchecked"],
[data-state="unchecked"] {
  background-color: rgb(229 231 235) !important; /* gray-200 */
  border-color: rgb(229 231 235) !important; /* gray-200 */
}

/* Dark mode switches */
.dark button[role="switch"][data-state="unchecked"],
.dark [data-state="unchecked"] {
  background-color: rgb(75 85 99) !important; /* gray-600 */
  border-color: rgb(75 85 99) !important; /* gray-600 */
}

/* Override button text colors - force white on primary buttons */
.btn-primary,
button.btn-primary {
  color: white !important;
  background-color: rgb(59 130 246) !important; /* blue-500 */
}

.btn-primary:hover,
button.btn-primary:hover {
  background-color: rgb(37 99 235) !important; /* blue-600 */
}

/* Override any yellow button states */
button:focus-visible {
  --tw-ring-color: rgb(59 130 246) !important; /* blue-500 */
  outline-color: rgb(59 130 246) !important; /* blue-500 */
}

/* Remove yellow from any toggle states */
[data-state="on"] {
  background-color: rgb(219 234 254) !important; /* blue-100 */
  color: rgb(30 64 175) !important; /* blue-800 */
}

/* Force all text to be proper colors - no yellow */
button, .btn-primary, .btn-secondary, .btn-accent {
  color: inherit !important;
}

.btn-primary {
  color: white !important;
}
