
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 51 65 85;

    --card: 255 255 255;
    --card-foreground: 51 65 85;

    --popover: 255 255 255;
    --popover-foreground: 51 65 85;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 241 245 249;
    --secondary-foreground: 51 65 85;

    --muted: 248 250 252;
    --muted-foreground: 100 116 139;

    --accent: 219 234 254;
    --accent-foreground: 30 64 175;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 1rem;

    /* Custom meditation app colors */
    --meditation-blue: 147 197 253;
    --meditation-light-blue: 219 234 254;
    --meditation-soft-white: 248 250 252;
    --meditation-glass: rgba(255, 255, 255, 0.1);
    --meditation-glass-border: rgba(255, 255, 255, 0.2);
  }

  .dark {
    --background: 17 24 39; /* Equivalent to bg-gray-900 */
    --foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --card: 31 41 55; /* Equivalent to bg-gray-800 */
    --card-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --popover: 31 41 55; /* Equivalent to bg-gray-800 */
    --popover-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --primary: 96 165 250; /* Equivalent to blue-400 */
    --primary-foreground: 17 24 39; /* Equivalent to bg-gray-900 for contrast */

    --secondary: 55 65 81; /* Equivalent to bg-gray-700 */
    --secondary-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --muted: 55 65 81; /* Equivalent to bg-gray-700 */
    --muted-foreground: 156 163 175; /* Equivalent to text-gray-400 */

    --accent: 59 130 246; /* Equivalent to blue-500 */
    --accent-foreground: 249 250 251; /* Equivalent to text-gray-50 */

    --destructive: 220 38 38; /* Equivalent to red-600 */
    --destructive-foreground: 249 250 251; /* Equivalent to text-gray-50 */

    --border: 55 65 81; /* Equivalent to border-gray-700 */
    --input: 55 65 81; /* Equivalent to bg-gray-700 for inputs */
    --ring: 96 165 250; /* Equivalent to blue-400 */

    /* Dark mode meditation colors - adjusted for better dark theme */
    --meditation-blue: 59 130 246; /* blue-500 */
    --meditation-light-blue: 37 99 235; /* blue-600 */
    --meditation-soft-white: 17 24 39; /* gray-900 */
    --meditation-glass: rgba(31, 41, 55, 0.5); /* gray-800 with 0.5 opacity */
    --meditation-glass-border: rgba(55, 65, 81, 0.7); /* gray-700 with 0.7 opacity */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply text-gray-800 dark:text-gray-200 min-h-screen;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    /* Beautiful gradient background */
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f8fafc 100%);
  }

  .dark body {
    /* Dark mode gradient */
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  }
}

@layer components {
  /* Consistent container for all pages - optimized spacing */
  .page-container {
    @apply min-h-screen px-6 py-6 max-w-xl mx-auto;
  }

  /* Enhanced glass morphism cards */
  .card {
    @apply rounded-2xl p-6 w-full transition-all duration-300 hover:scale-[1.02];
    /* Glass morphism effect */
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.15),
      0 4px 16px rgba(31, 38, 135, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .dark .card {
    background: rgba(31, 41, 55, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.3);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .card:hover {
    box-shadow:
      0 12px 40px rgba(31, 38, 135, 0.2),
      0 6px 20px rgba(31, 38, 135, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .dark .card:hover {
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 6px 20px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Primary button - stunning gradient styling */
  .btn-primary {
    @apply text-white rounded-xl px-6 py-3 font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95 relative overflow-hidden;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.4),
      0 2px 8px rgba(59, 130, 246, 0.2);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e3a8a 100%);
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.5),
      0 3px 12px rgba(59, 130, 246, 0.3);
    transform: translateY(-2px);
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  /* Secondary button - glass morphism styling */
  .btn-secondary {
    @apply text-gray-600 dark:text-gray-300 rounded-xl px-6 py-3 font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 active:scale-95;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .dark .btn-secondary {
    background: rgba(55, 65, 81, 0.7);
    border: 1px solid rgba(75, 85, 99, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  .dark .btn-secondary:hover {
    background: rgba(55, 65, 81, 0.9);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  }

  /* Accent button - soft gradient styling */
  .btn-accent {
    @apply text-blue-600 dark:text-blue-400 rounded-xl px-6 py-3 font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.2) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  .btn-accent:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 197, 253, 0.3) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }

  /* Consistent section spacing */
  .section-spacing {
    @apply mb-8;
  }

  /* Consistent card spacing */
  .card-spacing {
    @apply mb-6;
  }

  /* Icon container - stunning gradient styling */
  .icon-container {
    @apply w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.4),
      0 2px 8px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .icon-container:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.5),
      0 3px 12px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Small icon container */
  .icon-container-sm {
    @apply w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    box-shadow:
      0 3px 12px rgba(59, 130, 246, 0.3),
      0 1px 6px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .icon-container-sm:hover {
    transform: scale(1.1);
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.4),
      0 2px 8px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Screen reader only content */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
  }

  /* Safe area for mobile devices */
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Mobile-first responsive utilities */
  @media (max-width: 640px) {
    .page-container {
      @apply px-4 py-4;
    }

    .card {
      @apply p-4;
    }

    .section-spacing {
      @apply mb-6;
    }
  }

  /* Enhanced focus styles for better accessibility */
  *:focus-visible {
    outline: 2px solid rgb(59 130 246);
    outline-offset: 2px;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Reduced motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Beautiful animations */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Beautiful loading states */
.loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

/* COMPREHENSIVE COLOR SYSTEM FIXES */

/* Remove ALL yellow colors globally */
*,
*::before,
*::after {
  /* Override any yellow focus rings */
  --tw-ring-color: rgb(59 130 246) !important; /* blue-500 */
}

/* Fix all switch states */
button[role="switch"],
[role="switch"] {
  /* Ensure proper focus states */
  --tw-ring-color: rgb(59 130 246) !important;
}

button[role="switch"][data-state="checked"],
[data-state="checked"] {
  background-color: rgb(59 130 246) !important; /* blue-500 */
  border-color: rgb(59 130 246) !important;
}

button[role="switch"][data-state="unchecked"],
[data-state="unchecked"] {
  background-color: rgb(229 231 235) !important; /* gray-200 */
  border-color: rgb(229 231 235) !important;
}

/* Dark mode switches */
.dark button[role="switch"][data-state="unchecked"],
.dark [data-state="unchecked"] {
  background-color: rgb(75 85 99) !important; /* gray-600 */
  border-color: rgb(75 85 99) !important;
}

/* Fix all button variants */
.btn-primary {
  background-color: rgb(59 130 246) !important; /* blue-500 */
  color: white !important;
  border: none !important;
}

.btn-primary:hover {
  background-color: rgb(37 99 235) !important; /* blue-600 */
}

.btn-primary:focus-visible {
  --tw-ring-color: rgb(59 130 246) !important;
  box-shadow: 0 0 0 2px rgb(59 130 246) !important;
}

/* Fix accent buttons */
.btn-accent {
  background-color: rgb(239 246 255) !important; /* blue-50 */
  color: rgb(37 99 235) !important; /* blue-600 */
}

.dark .btn-accent {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: rgb(96 165 250) !important; /* blue-400 */
}

/* Override focus states globally */
*:focus-visible {
  --tw-ring-color: rgb(59 130 246) !important;
  outline-color: rgb(59 130 246) !important;
}

/* Remove yellow from toggle states */
[data-state="on"] {
  background-color: rgb(219 234 254) !important; /* blue-100 */
  color: rgb(30 64 175) !important; /* blue-800 */
}

/* Fix radio buttons and checkboxes */
input[type="radio"]:checked,
input[type="checkbox"]:checked {
  background-color: rgb(59 130 246) !important;
  border-color: rgb(59 130 246) !important;
}
