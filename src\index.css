
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 51 65 85;

    --card: 255 255 255;
    --card-foreground: 51 65 85;

    --popover: 255 255 255;
    --popover-foreground: 51 65 85;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 241 245 249;
    --secondary-foreground: 51 65 85;

    --muted: 248 250 252;
    --muted-foreground: 100 116 139;

    --accent: 219 234 254;
    --accent-foreground: 30 64 175;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 1rem;

    /* Custom meditation app colors */
    --meditation-blue: 147 197 253;
    --meditation-light-blue: 219 234 254;
    --meditation-soft-white: 248 250 252;
    --meditation-glass: rgba(255, 255, 255, 0.1);
    --meditation-glass-border: rgba(255, 255, 255, 0.2);
  }

  .dark {
    --background: 17 24 39; /* Equivalent to bg-gray-900 */
    --foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --card: 31 41 55; /* Equivalent to bg-gray-800 */
    --card-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --popover: 31 41 55; /* Equivalent to bg-gray-800 */
    --popover-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --primary: 96 165 250; /* Equivalent to blue-400 */
    --primary-foreground: 17 24 39; /* Equivalent to bg-gray-900 for contrast */

    --secondary: 55 65 81; /* Equivalent to bg-gray-700 */
    --secondary-foreground: 229 231 235; /* Equivalent to text-gray-200 */

    --muted: 55 65 81; /* Equivalent to bg-gray-700 */
    --muted-foreground: 156 163 175; /* Equivalent to text-gray-400 */

    --accent: 59 130 246; /* Equivalent to blue-500 */
    --accent-foreground: 249 250 251; /* Equivalent to text-gray-50 */

    --destructive: 220 38 38; /* Equivalent to red-600 */
    --destructive-foreground: 249 250 251; /* Equivalent to text-gray-50 */

    --border: 55 65 81; /* Equivalent to border-gray-700 */
    --input: 55 65 81; /* Equivalent to bg-gray-700 for inputs */
    --ring: 96 165 250; /* Equivalent to blue-400 */

    /* Dark mode meditation colors - adjusted for better dark theme */
    --meditation-blue: 59 130 246; /* blue-500 */
    --meditation-light-blue: 37 99 235; /* blue-600 */
    --meditation-soft-white: 17 24 39; /* gray-900 */
    --meditation-glass: rgba(31, 41, 55, 0.5); /* gray-800 with 0.5 opacity */
    --meditation-glass-border: rgba(55, 65, 81, 0.7); /* gray-700 with 0.7 opacity */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground min-h-screen;
    font-family: 'Inter', sans-serif;
    font-weight: 300;
  }
  .light body {
     @apply bg-gradient-to-br from-blue-50 via-white to-blue-100;
  }
  .dark body {
    @apply bg-gradient-to-br from-gray-900 via-gray-900 to-slate-800;
  }
}

@layer components {
  .glass-card {
    @apply backdrop-blur-xl bg-white/80 rounded-3xl shadow-2xl shadow-blue-100/50 border border-white/15;
  }
  
  .dark .glass-card {
    @apply backdrop-blur-xl bg-gray-800/80 rounded-3xl shadow-2xl shadow-black/30 border border-gray-700/30;
  }
  
  .glass-button {
    @apply backdrop-blur-sm bg-white/30 hover:bg-white/40 border border-white/40 rounded-xl transition-all duration-300 text-gray-800;
  }
  
  .dark .glass-button {
    @apply backdrop-blur-sm bg-gray-800/50 hover:bg-gray-800/70 border border-gray-700/40 rounded-xl transition-all duration-300 text-gray-200;
  }

  .meditation-glow {
    box-shadow: 0 0 50px rgba(147, 197, 253, 0.2);
  }
  
  .dark .meditation-glow {
    box-shadow: 0 0 50px rgba(59, 130, 246, 0.2);
  }
}

/* Simple fade-in animation only */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}
