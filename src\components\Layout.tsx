
import { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, User, Clock, MessageSquare } from 'lucide-react';

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();

  const navItems = [
    { icon: Home, label: 'Home', path: '/' },
    { icon: User, label: 'Profile', path: '/profile' },
    { icon: Clock, label: 'Sessions', path: '/sessions' },
    { icon: MessageSquare, label: 'Feedback', path: '/feedback' },
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Minimal Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-24 h-24 bg-blue-200/20 dark:bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-blue-300/15 dark:bg-blue-400/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-1/3 left-1/3 w-28 h-28 bg-blue-100/25 dark:bg-blue-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/3 w-16 h-16 bg-blue-200/20 dark:bg-blue-500/10 rounded-full blur-xl"></div>
      </div>

      {/* Main Content */}
      <main className="pb-24 relative z-10 min-h-screen flex flex-col">
        {children}
      </main>

      {/* Bottom Navigation - Clean & Minimal */}
      <nav className="fixed bottom-0 left-0 right-0 z-50">
        <div className="glass-card mx-3 mb-3 p-1 shadow-lg">
          <div className="flex justify-around items-center">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex flex-col items-center p-3 rounded-2xl transition-all duration-200 ${
                    isActive
                      ? 'bg-blue-500/15 text-blue-600 dark:text-blue-400 scale-105'
                      : 'text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 hover:bg-blue-50/30 dark:hover:bg-blue-900/30'
                  }`}
                  aria-current={isActive ? 'page' : undefined}
                >
                  <Icon size={18} strokeWidth={1.5} />
                  <span className="text-xs mt-1 font-light">{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>
    </div>
  );
};

export default Layout;
