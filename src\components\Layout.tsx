
import { ReactNode, useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, User, Clock, MessageSquare } from 'lucide-react';

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const checkDarkMode = () => {
      setIsDark(document.documentElement.classList.contains('dark'));
    };

    checkDarkMode();
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

    return () => observer.disconnect();
  }, []);

  const navItems = [
    { icon: Home, label: 'Home', path: '/', ariaLabel: 'Navigate to Home page' },
    { icon: User, label: 'Profile', path: '/profile', ariaLabel: 'Navigate to Profile page' },
    { icon: Clock, label: 'Sessions', path: '/sessions', ariaLabel: 'Navigate to Sessions page' },
    { icon: MessageSquare, label: 'Feedback', path: '/feedback', ariaLabel: 'Navigate to Feedback page' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Main Content */}
      <main className="pb-20 min-h-screen" role="main">
        {children}
      </main>

      {/* Bottom Navigation - Glass Morphism */}
      <nav
        className="fixed bottom-0 left-0 right-0 z-50 safe-area-pb"
        role="navigation"
        aria-label="Main navigation"
        style={isDark ? {
          background: 'rgba(15, 23, 42, 0.9)',
          backdropFilter: 'blur(20px)',
          borderTop: '1px solid rgba(30, 41, 59, 0.5)',
          boxShadow: '0 -8px 32px rgba(0, 0, 0, 0.5)'
        } : {
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(20px)',
          borderTop: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 -8px 32px rgba(31, 38, 135, 0.15)'
        }}
      >
        <div className="max-w-xl mx-auto px-4 py-2">
          <div className="flex justify-around items-center">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex flex-col items-center p-3 rounded-xl transition-all duration-300 min-w-[60px] ${
                    isActive
                      ? 'text-blue-600 dark:text-blue-400 scale-110'
                      : 'text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:scale-110'
                  }`}
                  style={isActive ? {
                    background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.2) 100%)',
                    boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2)',
                    border: '1px solid rgba(59, 130, 246, 0.2)'
                  } : {}}
                  aria-current={isActive ? 'page' : undefined}
                  aria-label={item.ariaLabel}
                  title={item.ariaLabel}
                >
                  <Icon size={20} strokeWidth={1.5} aria-hidden="true" />
                  <span className="text-xs mt-1 font-medium">{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>
    </div>
  );
};

export default Layout;
