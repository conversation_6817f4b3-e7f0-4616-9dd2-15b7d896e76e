
import { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, User, Clock, MessageSquare } from 'lucide-react';

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();

  const navItems = [
    { icon: Home, label: 'Home', path: '/', ariaLabel: 'Navigate to Home page' },
    { icon: User, label: 'Profile', path: '/profile', ariaLabel: 'Navigate to Profile page' },
    { icon: Clock, label: 'Sessions', path: '/sessions', ariaLabel: 'Navigate to Sessions page' },
    { icon: MessageSquare, label: 'Feedback', path: '/feedback', ariaLabel: 'Navigate to Feedback page' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Main Content */}
      <main className="pb-20 min-h-screen" role="main">
        {children}
      </main>

      {/* Bottom Navigation - Enhanced Accessibility */}
      <nav
        className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 safe-area-pb"
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="max-w-xl mx-auto px-4 py-2">
          <div className="flex justify-around items-center">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex flex-col items-center p-3 rounded-xl transition-all duration-200 min-w-[60px] ${
                    isActive
                      ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 scale-105'
                      : 'text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:scale-105'
                  }`}
                  aria-current={isActive ? 'page' : undefined}
                  aria-label={item.ariaLabel}
                  title={item.ariaLabel}
                >
                  <Icon size={20} strokeWidth={1.5} aria-hidden="true" />
                  <span className="text-xs mt-1 font-medium">{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </nav>
    </div>
  );
};

export default Layout;
